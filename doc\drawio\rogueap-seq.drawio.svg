<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="461px" height="281px" viewBox="-0.5 -0.5 461 281" content="&lt;mxfile&gt;&lt;diagram id=&quot;ikWz-W6OypJqwVNcDCaw&quot; name=&quot;rogue-ap-deauth-theory&quot;&gt;7Vlbc5s6EP41fnSGqx0/+tLmdMadSeN2enmTQQZNBaJC+HJ+/VmBuBPHpDhJz/TJaJFWaL9vP63kkbkMjnccRf5H5mI6MjT3ODJXI8PQtZkNP9Jyyiy2NckMHieu6lQaNuRfnI9U1oS4OK51FIxRQaK60WFhiB1RsyHO2aHebcdofdYIebhl2DiItq1fiSv8zHprTEv7P5h4fj6zPpllbwKUd1YriX3kskPFZL4bmUvOmMieguMSUxm8PC7x8dP0+7fPv9a/DI62429fuUHGmbP3fYYUS+A4FMO6NjLXe0QTFa/N57larjjlMYSVR/IxCeia7DAlIbQWEeYkwAJzeEOV+b60LQ4+EXgTIUcOPQC7wOaLgEJLh0cAXCAYwos2pSiKyTadVQMLx07CY7LHDzjOeCWtLBFypmXBF2m8MEIqknvMBT5W+KEidocZfDw/QRf11rAU+Ir9t6p5KKmka8rmV2hUkAYp+nqF6xIieFAodSPGFqcftrX6kGgfN1/4fn/cfzmNzRZiLbgqQY4YCUX6CfZiZK8aqDEufOaxENEqbhfG0ugbS8uuhXLSFcp2JHVrgEh2k6EdyjscJpLchja//5sFBXLTZybB7bWgs99KEhQcunIWGNq19ERrhfKBecnfFGjCpr1iDnwny/Bhpm/Wnr778GlmmfHaHU9fIQfOMui5KTC7cCMYIgU6I9lvS5XrJFBfzinxQrBtmRAsgBcUbTFdIOenx1kSuktGmYxtyNIkwaE7l+WsHEGZ8zMzvSc099uPuTFLuIOfrhME4h4+509hgd1aGd3GropOLlocUyQgLetFewc8yt29JGGluJrUc0pv5kq2SDWqWto2HFnGE46yKLQcASDoVOmmkuTxD9Yb88xqJTc8ZB5LKhYBfD47rRY7lxTF8JWaNAPFDM2UByOOAtzirYR1LXlZl1ykuOsA19JUb5I6IK4rfYD0guKiUorbOtLm7dkka6pEcfJTk4yqh6su9RhrN4ZpmoMwEPbvm8m0PortdjEWo6aoDIBkv6rl93XGkTwhTktpYkgHkXdSw1Kb6tN/H30V9ZjVt5E8Gfurh/WEo4HUw3zkg6+qHpP/h3rkqfOm1GN6kztSiGo3+ouJye0fXrScq2mrRcu54uY1ZMey6zWAYT9Tdoq69zFHQ8nOtC47edV1VdmZtdi5wigRPtAEaChY+AdoTp5hb0pzXrZi0duHzHmJInbTWARBEsomkag+LkE9EJRKhN183DZmNBF4zh3170JqLVsSZBfFfjHC48glMEtDzPpp1eXHWb1xNaCb7QNtcR9cPdEOcbPZDVs7/dJrfY3IjT9kkk+ogePImFD4rsUWSoKJJ5/CtDqAYoE5RHU6EFgQ6LOfOuq4IOqDt2DRb4KtvTzYZuMutLiY69pqXgbsC44VlXu7FY4FZ6dmGK8Vr+bdcdfFmTVMuKBZ/hmXKWD5l6b57j8=&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><rect x="170" y="0" width="100" height="40" fill="#ffffff" stroke="#000000" pointer-events="all"/><path d="M 220 40 L 220 220" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 20px; margin-left: 171px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">STA</div></div></div></foreignObject><text x="220" y="24" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">STA</text></switch></g><rect x="215" y="60" width="10" height="140" fill="#ffffff" stroke="#000000" pointer-events="all"/><rect x="0" y="0" width="100" height="40" fill="#ffffff" stroke="#000000" pointer-events="all"/><path d="M 50 40 L 50 280" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 20px; margin-left: 1px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Genuine AP</div></div></div></foreignObject><text x="50" y="24" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Genuine AP</text></switch></g><rect x="45" y="60" width="10" height="200" fill="#ffffff" stroke="#000000" pointer-events="all"/><rect x="330" y="0" width="100" height="40" fill="#ffffff" stroke="#000000" pointer-events="all"/><path d="M 380 40 L 380 280" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 20px; margin-left: 331px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Rogue AP</div></div></div></foreignObject><text x="380" y="24" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Rogue AP</text></switch></g><rect x="375" y="90" width="10" height="100" fill="#ffffff" stroke="#000000" pointer-events="all"/><path d="M 215 110 L 150 110 Q 140 110 130 110 L 63.12 110" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 56.12 110 L 63.12 106.5 L 63.12 113.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 111px; margin-left: 141px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">Class 2 or 3 frame</div></div></div></foreignObject><text x="141" y="114" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">Class 2 or 3 frame</text></switch></g><path d="M 225 110 L 315 110 Q 325 110 335 110 L 368.63 110" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 373.88 110 L 366.88 113.5 L 368.63 110 L 366.88 106.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 111px; margin-left: 291px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">Class 2 or 3 frame</div></div></div></foreignObject><text x="291" y="114" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">Class 2 or 3 frame</text></switch></g><path d="M 375 180 L 315 180 Q 305 180 295 180 L 233.12 180" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 226.12 180 L 233.12 176.5 L 233.12 183.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 181px; margin-left: 304px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">Deauthenticaton frame</div></div></div></foreignObject><text x="304" y="184" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">Deauthenticaton frame</text></switch></g><rect x="30" y="50" width="220" height="40" rx="5" ry="5" fill="#ffffff" stroke="#000000" stroke-dasharray="3 3" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 70px; margin-left: 140px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">Authenticated communication</div></div></div></foreignObject><text x="140" y="74" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Authenticated communication</text></switch></g><rect x="300" y="120" width="160" height="40" rx="5" ry="5" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 127px; margin-left: 380px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">STA is not authenticated <br />nor associated with this AP</div></div></div></foreignObject><text x="380" y="139" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">STA is not authenticated...</text></switch></g><path d="M 240 200 L 200 240 M 200 200 L 240 240" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Viewer does not support full SVG 1.1</text></a></switch></svg>