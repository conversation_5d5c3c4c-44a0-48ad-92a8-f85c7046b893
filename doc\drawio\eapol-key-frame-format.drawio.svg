<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="570px" height="571px" viewBox="-0.5 -0.5 570 571" content="&lt;mxfile&gt;&lt;diagram name=&quot;EAPOL-Key-frame-structure&quot; id=&quot;l1nS0nFMuED0pGsFOT54&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><path d="M 258 -200 L 248 -200 Q 238 -200 238 -190 L 238 20 Q 238 30 228 30 L 223 30 Q 218 30 228 30 L 233 30 Q 238 30 238 40 L 238 250 Q 238 260 248 260 L 258 260" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="1 2" transform="rotate(90,238,30)" pointer-events="all"/><rect x="8" y="50" width="60" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 70px; margin-left: 9px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Framce Conrol</div></div></div></foreignObject><text x="38" y="74" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Framce Con...</text></switch></g><rect x="68" y="50" width="60" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 70px; margin-left: 69px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Duration</div></div></div></foreignObject><text x="98" y="74" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Duration</text></switch></g><rect x="128" y="50" width="80" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 70px; margin-left: 129px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">STA/AP MAC (destination)</div></div></div></foreignObject><text x="168" y="74" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">STA/AP MAC (d...</text></switch></g><rect x="208" y="50" width="80" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 70px; margin-left: 209px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">STA/AP MAC <br />(source)</div></div></div></foreignObject><text x="248" y="74" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">STA/AP MAC...</text></switch></g><rect x="288" y="50" width="60" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 70px; margin-left: 289px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">BSSID</div></div></div></foreignObject><text x="318" y="74" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">BSSID</text></switch></g><rect x="348" y="50" width="60" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 70px; margin-left: 349px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Sequence control</div></div></div></foreignObject><text x="378" y="74" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Sequence c...</text></switch></g><rect x="408" y="50" width="60" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 70px; margin-left: 409px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">QoS Data</div></div></div></foreignObject><text x="438" y="74" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">QoS Data</text></switch></g><path d="M 14.37 40 L 61.63 40" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 9.12 40 L 16.12 36.5 L 14.37 40 L 16.12 43.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 66.88 40 L 59.88 43.5 L 61.63 40 L 59.88 36.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 40px; margin-left: 38px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">2</div></div></div></foreignObject><text x="38" y="43" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">2</text></switch></g><path d="M 74.37 40 L 121.63 40" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 69.12 40 L 76.12 36.5 L 74.37 40 L 76.12 43.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 126.88 40 L 119.88 43.5 L 121.63 40 L 119.88 36.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 40px; margin-left: 98px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">2</div></div></div></foreignObject><text x="98" y="43" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">2</text></switch></g><path d="M 134.37 40 L 201.63 40" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 129.12 40 L 136.12 36.5 L 134.37 40 L 136.12 43.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 206.88 40 L 199.88 43.5 L 201.63 40 L 199.88 36.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 40px; margin-left: 168px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">6</div></div></div></foreignObject><text x="168" y="43" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">6</text></switch></g><path d="M 214.37 40 L 281.63 40" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 209.12 40 L 216.12 36.5 L 214.37 40 L 216.12 43.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 286.88 40 L 279.88 43.5 L 281.63 40 L 279.88 36.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 40px; margin-left: 248px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">6</div></div></div></foreignObject><text x="248" y="43" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">6</text></switch></g><path d="M 294.37 40 L 341.63 40" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 289.12 40 L 296.12 36.5 L 294.37 40 L 296.12 43.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 346.88 40 L 339.88 43.5 L 341.63 40 L 339.88 36.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 40px; margin-left: 318px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">6</div></div></div></foreignObject><text x="318" y="43" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">6</text></switch></g><path d="M 354.37 40 L 401.63 40" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 349.12 40 L 356.12 36.5 L 354.37 40 L 356.12 43.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 406.88 40 L 399.88 43.5 L 401.63 40 L 399.88 36.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 40px; margin-left: 378px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">2</div></div></div></foreignObject><text x="378" y="43" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">2</text></switch></g><path d="M 414.37 40 L 461.63 40" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 409.12 40 L 416.12 36.5 L 414.37 40 L 416.12 43.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 466.88 40 L 459.88 43.5 L 461.63 40 L 459.88 36.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 40px; margin-left: 438px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">0 or 2</div></div></div></foreignObject><text x="438" y="43" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">0 or 2</text></switch></g><path d="M 468 90 L 8 170" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 528 90 L 188 170" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><rect x="468" y="50" width="60" height="40" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 70px; margin-left: 469px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Body</div></div></div></foreignObject><text x="498" y="74" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Body</text></switch></g><rect x="198" y="0" width="80" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 10px; margin-left: 199px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">MAC header</div></div></div></foreignObject><text x="238" y="14" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">MAC header</text></switch></g><rect x="8" y="170" width="60" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 190px; margin-left: 9px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">LLC snap header</div></div></div></foreignObject><text x="38" y="194" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">LLC snap h...</text></switch></g><path d="M 14.37 160 L 61.63 160" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 9.12 160 L 16.12 156.5 L 14.37 160 L 16.12 163.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 66.88 160 L 59.88 163.5 L 61.63 160 L 59.88 156.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 160px; margin-left: 38px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">6</div></div></div></foreignObject><text x="38" y="163" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">6</text></switch></g><rect x="68" y="170" width="60" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 190px; margin-left: 69px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">EtherType<br /><i>0x888e</i></div></div></div></foreignObject><text x="98" y="194" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">EtherType...</text></switch></g><path d="M 74.37 160 L 121.63 160" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 69.12 160 L 76.12 156.5 L 74.37 160 L 76.12 163.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 126.88 160 L 119.88 163.5 L 121.63 160 L 119.88 156.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 160px; margin-left: 98px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">2</div></div></div></foreignObject><text x="98" y="163" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">2</text></switch></g><path d="M 128 210 L 8 290" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 188 210 L 328 290" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><rect x="128" y="170" width="60" height="40" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 190px; margin-left: 129px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">EAPOL packet</div></div></div></foreignObject><text x="158" y="194" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">EAPOL pack...</text></switch></g><rect x="8" y="290" width="60" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 310px; margin-left: 9px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Version</div></div></div></foreignObject><text x="38" y="314" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Version</text></switch></g><path d="M 14.37 280 L 61.63 280" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 9.12 280 L 16.12 276.5 L 14.37 280 L 16.12 283.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 66.88 280 L 59.88 283.5 L 61.63 280 L 59.88 276.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 280px; margin-left: 38px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">1</div></div></div></foreignObject><text x="38" y="283" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">1</text></switch></g><rect x="68" y="290" width="100" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 310px; margin-left: 69px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Packet type<br /><i>EAPOL-Key</i></div></div></div></foreignObject><text x="118" y="314" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Packet type...</text></switch></g><path d="M 74.37 280 L 161.63 280" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 69.12 280 L 76.12 276.5 L 74.37 280 L 76.12 283.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 166.88 280 L 159.88 283.5 L 161.63 280 L 159.88 276.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 280px; margin-left: 118px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">1</div></div></div></foreignObject><text x="118" y="283" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">1</text></switch></g><rect x="168" y="290" width="80" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 310px; margin-left: 169px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Packet body length</div></div></div></foreignObject><text x="208" y="314" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Packet body l...</text></switch></g><path d="M 328 330 L 568 410" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 248 330 L 8 410" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><rect x="248" y="290" width="80" height="40" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 310px; margin-left: 249px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Packet<br />body</div></div></div></foreignObject><text x="288" y="314" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Packet...</text></switch></g><path d="M 174.37 280 L 241.63 280" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 169.12 280 L 176.12 276.5 L 174.37 280 L 176.12 283.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 246.88 280 L 239.88 283.5 L 241.63 280 L 239.88 276.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 280px; margin-left: 208px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">2</div></div></div></foreignObject><text x="208" y="283" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">2</text></switch></g><rect x="8" y="410" width="60" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 430px; margin-left: 9px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Descriptor type</div></div></div></foreignObject><text x="38" y="434" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Descriptor...</text></switch></g><path d="M 14.37 400 L 61.63 400" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 9.12 400 L 16.12 396.5 L 14.37 400 L 16.12 403.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 66.88 400 L 59.88 403.5 L 61.63 400 L 59.88 396.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 400px; margin-left: 38px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">1</div></div></div></foreignObject><text x="38" y="403" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">1</text></switch></g><rect x="68" y="410" width="60" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 430px; margin-left: 69px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Key information</div></div></div></foreignObject><text x="98" y="434" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Key inform...</text></switch></g><rect x="128" y="410" width="50" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 48px; height: 1px; padding-top: 430px; margin-left: 129px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Key length</div></div></div></foreignObject><text x="153" y="434" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Key leng...</text></switch></g><rect x="178" y="410" width="60" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 430px; margin-left: 179px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Key replay counter</div></div></div></foreignObject><text x="208" y="434" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Key replay...</text></switch></g><rect x="238" y="410" width="50" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 48px; height: 1px; padding-top: 430px; margin-left: 239px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Key nonce</div></div></div></foreignObject><text x="263" y="434" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Key nonce</text></switch></g><rect x="288" y="410" width="30" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 28px; height: 1px; padding-top: 430px; margin-left: 289px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Key IV<span style="color: rgba(0 , 0 , 0 , 0) ; font-family: monospace ; font-size: 0px">%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22Key%20nonce%22%20style%3D%22rounded%3D0%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%23dae8fc%3BstrokeColor%3D%236c8ebf%3B%22%20vertex%3D%221%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%22280%22%20y%3D%22440%22%20width%3D%2260%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E</span></div></div></div></foreignObject><text x="303" y="434" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Key I...</text></switch></g><rect x="318" y="410" width="50" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 48px; height: 1px; padding-top: 430px; margin-left: 319px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Key RSC</div></div></div></foreignObject><text x="343" y="434" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Key RSC</text></switch></g><rect x="368" y="410" width="60" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 430px; margin-left: 369px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Reserver</div></div></div></foreignObject><text x="398" y="434" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Reserver</text></switch></g><rect x="428" y="410" width="40" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 430px; margin-left: 429px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Key MIC</div></div></div></foreignObject><text x="448" y="434" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Key MIC</text></switch></g><rect x="468" y="410" width="60" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 430px; margin-left: 469px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Key data length</div></div></div></foreignObject><text x="498" y="434" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Key data l...</text></switch></g><path d="M 528 450 L 8 530" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 568 450 L 228 530" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><rect x="528" y="410" width="40" height="40" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 430px; margin-left: 529px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Key data</div></div></div></foreignObject><text x="548" y="434" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Key da...</text></switch></g><path d="M 74.37 400 L 121.63 400" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 69.12 400 L 76.12 396.5 L 74.37 400 L 76.12 403.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 126.88 400 L 119.88 403.5 L 121.63 400 L 119.88 396.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 400px; margin-left: 98px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">2</div></div></div></foreignObject><text x="98" y="403" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">2</text></switch></g><path d="M 474.37 400 L 521.63 400" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 469.12 400 L 476.12 396.5 L 474.37 400 L 476.12 403.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 526.88 400 L 519.88 403.5 L 521.63 400 L 519.88 396.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 400px; margin-left: 498px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">2</div></div></div></foreignObject><text x="498" y="403" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">2</text></switch></g><path d="M 134.37 400 L 171.63 400" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 129.12 400 L 136.12 396.5 L 134.37 400 L 136.12 403.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 176.88 400 L 169.88 403.5 L 171.63 400 L 169.88 396.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 400px; margin-left: 153px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">2<span style="color: rgba(0 , 0 , 0 , 0) ; font-family: monospace ; font-size: 0px ; background-color: rgb(248 , 249 , 250)">%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%221%22%20style%3D%22endArrow%3Dclassic%3BstartArrow%3Dclassic%3Bhtml%3D1%3B%22%20edge%3D%221%22%20parent%3D%221%22%3E%3CmxGeometry%20width%3D%2250%22%20height%3D%2250%22%20relative%3D%221%22%20as%3D%22geometry%22%3E%3CmxPoint%20x%3D%22160%22%20y%3D%22640%22%20as%3D%22sourcePoint%22%2F%3E%3CmxPoint%20x%3D%22220%22%20y%3D%22640%22%20as%3D%22targetPoint%22%2F%3E%3C%2FmxGeometry%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E</span></div></div></div></foreignObject><text x="153" y="403" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">2%3CmxGrap...</text></switch></g><path d="M 184.37 400 L 231.63 400" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 179.12 400 L 186.12 396.5 L 184.37 400 L 186.12 403.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 236.88 400 L 229.88 403.5 L 231.63 400 L 229.88 396.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 400px; margin-left: 208px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">8</div></div></div></foreignObject><text x="208" y="403" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">8</text></switch></g><path d="M 244.37 400 L 281.63 400" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 239.12 400 L 246.12 396.5 L 244.37 400 L 246.12 403.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 286.88 400 L 279.88 403.5 L 281.63 400 L 279.88 396.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 400px; margin-left: 263px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">32</div></div></div></foreignObject><text x="263" y="403" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">32</text></switch></g><path d="M 294.37 400 L 311.63 400" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 289.12 400 L 296.12 396.5 L 294.37 400 L 296.12 403.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 316.88 400 L 309.88 403.5 L 311.63 400 L 309.88 396.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 400px; margin-left: 303px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">16</div></div></div></foreignObject><text x="303" y="403" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">16</text></switch></g><path d="M 324.37 400 L 361.63 400" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 319.12 400 L 326.12 396.5 L 324.37 400 L 326.12 403.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 366.88 400 L 359.88 403.5 L 361.63 400 L 359.88 396.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 400px; margin-left: 343px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">8</div></div></div></foreignObject><text x="343" y="403" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">8</text></switch></g><path d="M 374.37 400 L 421.63 400" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 369.12 400 L 376.12 396.5 L 374.37 400 L 376.12 403.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 426.88 400 L 419.88 403.5 L 421.63 400 L 419.88 396.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 400px; margin-left: 398px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">8</div></div></div></foreignObject><text x="398" y="403" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">8</text></switch></g><path d="M 434.37 400 L 461.63 400" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 429.12 400 L 436.12 396.5 L 434.37 400 L 436.12 403.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 466.88 400 L 459.88 403.5 L 461.63 400 L 459.88 396.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 400px; margin-left: 448px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">16</div></div></div></foreignObject><text x="448" y="403" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">16</text></switch></g><path d="M 148 150 L 138 150 Q 128 150 128 160 L 128 260 Q 128 270 118 270 L 113 270 Q 108 270 118 270 L 123 270 Q 128 270 128 280 L 128 380 Q 128 390 138 390 L 148 390" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="1 2" transform="rotate(90,128,270)" pointer-events="all"/><rect x="83" y="240" width="90" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 250px; margin-left: 84px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">EAPOL header</div></div></div></foreignObject><text x="128" y="254" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">EAPOL header</text></switch></g><rect x="8" y="530" width="40" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 550px; margin-left: 9px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Type<span style="color: rgba(0 , 0 , 0 , 0) ; font-family: monospace ; font-size: 0px">%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22Descriptor%20type%22%20style%3D%22rounded%3D0%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%23dae8fc%3BstrokeColor%3D%236c8ebf%3B%22%20vertex%3D%221%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%2240%22%20y%3D%22560%22%20width%3D%2260%22%20height%3D%2240%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E</span></div></div></div></foreignObject><text x="28" y="554" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Type%3...</text></switch></g><rect x="48" y="530" width="40" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 550px; margin-left: 49px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Length</div></div></div></foreignObject><text x="68" y="554" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Length</text></switch></g><rect x="88" y="530" width="40" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 550px; margin-left: 89px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">OUI</div></div></div></foreignObject><text x="108" y="554" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">OUI</text></switch></g><rect x="128" y="530" width="40" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 550px; margin-left: 129px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Data type</div></div></div></foreignObject><text x="148" y="554" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Data t...</text></switch></g><rect x="168" y="530" width="60" height="40" fill="#e1d5e7" stroke="#9673a6" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 550px; margin-left: 169px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Data</div></div></div></foreignObject><text x="198" y="554" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Data</text></switch></g><path d="M 14.37 520 L 41.63 520" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 9.12 520 L 16.12 516.5 L 14.37 520 L 16.12 523.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 46.88 520 L 39.88 523.5 L 41.63 520 L 39.88 516.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 520px; margin-left: 28px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">1</div></div></div></foreignObject><text x="28" y="523" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">1</text></switch></g><path d="M 54.37 520 L 81.63 520" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 49.12 520 L 56.12 516.5 L 54.37 520 L 56.12 523.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 86.88 520 L 79.88 523.5 L 81.63 520 L 79.88 516.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 520px; margin-left: 68px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">1<span style="color: rgba(0 , 0 , 0 , 0) ; font-family: monospace ; font-size: 0px ; background-color: rgb(248 , 249 , 250)">%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%228%22%20style%3D%22endArrow%3Dclassic%3BstartArrow%3Dclassic%3Bhtml%3D1%3B%22%20edge%3D%221%22%20parent%3D%221%22%3E%3CmxGeometry%20width%3D%2250%22%20height%3D%2250%22%20relative%3D%221%22%20as%3D%22geometry%22%3E%3CmxPoint%20x%3D%22310%22%20y%3D%22800%22%20as%3D%22sourcePoint%22%2F%3E%3CmxPoint%20x%3D%22370%22%20y%3D%22800%22%20as%3D%22targetPoint%22%2F%3E%3C%2FmxGeometry%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E</span></div></div></div></foreignObject><text x="68" y="523" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">1%3CmxGrap...</text></switch></g><path d="M 94.37 520 L 121.63 520" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 89.12 520 L 96.12 516.5 L 94.37 520 L 96.12 523.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 126.88 520 L 119.88 523.5 L 121.63 520 L 119.88 516.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 520px; margin-left: 108px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">3</div></div></div></foreignObject><text x="108" y="523" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">3</text></switch></g><path d="M 134.37 520 L 161.63 520" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 129.12 520 L 136.12 516.5 L 134.37 520 L 136.12 523.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 166.88 520 L 159.88 523.5 L 161.63 520 L 159.88 516.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 520px; margin-left: 148px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">1</div></div></div></foreignObject><text x="148" y="523" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">1</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Viewer does not support full SVG 1.1</text></a></switch></svg>