<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="777px" height="133px" viewBox="-0.5 -0.5 777 133" content="&lt;mxfile&gt;&lt;diagram name=&quot;deauth-frame-format&quot; id=&quot;gKO0oo0F4Ae3FQDf5PKa&quot;&gt;7Vvfk6I4EP5rfBwLCCA8rrrOXt1e3dVZV/ccSZTcIfFCHMf96y+BgIQ4JcysPxjFB0kHGtNff+lOEwdgsn59ZnAT/0YRTgaOhV4HYDpwHNsKPfElJftC4rl+IVgxgtRFB8Gc/MDlnUq6JQhn2oWc0oSTjS6MaJriiGsyyBjd6ZctaaI/dQNX2BDMI5iY0r8J4nEhDZzRQf4Nk1VcPtn2w6JnDcuL1UiyGCK6q4nA1wGYMEp5cbZ+neBEGq+0y1/P/6z2W/7t99mvo/EzpQgB/6lQNutySzUEhlP+c1U7heoXmGyVvWYMrrEQTWjKGU3U0Pm+tCej2xRhqdMagPEuJhzPNzCSvTvhQUIW83UiWrY4XZIkmdCEsvxesPTkR8gzofpfXOvx80PeIR5bkxeHkLc0gTLVC2Ycv9YcQJnkGdM15mwvLlG9rgJXeXfV3tV8RYnimpuUl0HlnatK8QEBcaJA6AAIMACZbhnkhKafHQnbuTEoXBMKnHGSKjQsiBDDWfbZcXGs07hUsosA4xnAzOmWRfh+MHFbTFuXxcQ3McH/bXEqUfET8fTxgomzlTyL7iO02H4DpODKE5ptGyDNJvOfC8Ny6UTRMRiQv/A9/4zmBs34ccTcFSSXsbeZXRnGxin6IrNc0YoSmGWkMB5k3BTXzN7NiBhpObJpwpqJvCMWKmUMJyL4veiZ9TGzqSf8QYn4ebVca2jVDtt2dcBAA4gsn9WVknpm29BrZA5NRcKcK8wNRTmolRU+gLOZtMnmYs+xGYkkFt/hQqyyNEBhQlapRFsAigVlxtLxiVjGfFEda4KQ1DEW8Y38gItcnyToRg4rH6g3HnjTDs5RumeTYtViTD1lUF/vHKOeNQRBEGgYlK73QZd5sr1h4Dc0N7TQ5TLD5wHWTAHvmsAnidaWsUZOeWnGmilkLxhb+uODsW9ZyMxD75qxJ4nWlrHGiuPSjB0ZwPp9YGzpj7fMWNcfhlcjbPAgbCeetSWsH1yZsGE/CVv644Owb7wxsDoRNqUpbsVWcc+MyJ+S49dL7p6kXFvuBldOj8uh94y7lWveMncdMATgauR1DGDvOto2S7Tg3Qta94SiczO2nyWoyh9vmbHmgja8GF8fBSgNi2aN/918dYMr89UsQLl94GsfClDAHlp6hL0cXx/lp25hsS1fTxL/3Hw1y0+9iK99KD/Z1wquLUpPWQw38hTRaLvObX3qhfiieHv+fVEKGOXFjiEwfQolmlmxR9IaWiPXFWvGwLMtHwROGPRiW8PIDoe+Xn5wLOEeyni1ieXYBhRb3A1Gb/vIh164O2bN6cyIfgZAgwN4h+1EN4EnMGtN4/n8l6kBqhgp10HTzauqUHUslKj9pHzMS/StMghmcd7oHq870M8y6VdG6xpWxza6ONa5cDLrRe9IexQiGoiiu+9VQcklO6yOQKfauzOik6WLM2dE5fzw4Gadm2LarO1w8nSahtemqVkk+hPDLN/MHFGEDeh6uhPQ2NR/vo2Xonn4A0hBrcPfaMDX/wE=&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><rect x="8" y="26" width="80" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 46px; margin-left: 9px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #333333; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Frame Control</div></div></div></foreignObject><text x="48" y="50" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">Frame Control</text></switch></g><rect x="88" y="26" width="80" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 46px; margin-left: 89px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #333333; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Duration</div></div></div></foreignObject><text x="128" y="50" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">Duration</text></switch></g><rect x="168" y="26" width="240" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 238px; height: 1px; padding-top: 46px; margin-left: 169px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #333333; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Destination address</div></div></div></foreignObject><text x="288" y="50" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">Destination address</text></switch></g><rect x="408" y="26" width="240" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 238px; height: 1px; padding-top: 46px; margin-left: 409px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #333333; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Source address</div></div></div></foreignObject><text x="528" y="50" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">Source address</text></switch></g><rect x="128" y="66" width="80" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 86px; margin-left: 129px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #333333; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Sequence<br />control</div></div></div></foreignObject><text x="168" y="90" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">Sequence...</text></switch></g><rect x="288" y="66" width="160" height="40" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 86px; margin-left: 289px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">FCS</div></div></div></foreignObject><text x="368" y="90" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">FCS</text></switch></g><path d="M 14.37 16 L 81.63 16" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 9.12 16 L 16.12 12.5 L 14.37 16 L 16.12 19.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 86.88 16 L 79.88 19.5 L 81.63 16 L 79.88 12.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 7px; margin-left: 49px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">2 bytes</div></div></div></foreignObject><text x="49" y="10" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">2 bytes</text></switch></g><path d="M 94.37 16 L 161.63 16" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 89.12 16 L 96.12 12.5 L 94.37 16 L 96.12 19.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 166.88 16 L 159.88 19.5 L 161.63 16 L 159.88 12.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 7px; margin-left: 129px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">2 bytes</div></div></div></foreignObject><text x="129" y="10" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">2 bytes</text></switch></g><path d="M 174.37 16 L 401.63 16" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 169.12 16 L 176.12 12.5 L 174.37 16 L 176.12 19.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 406.88 16 L 399.88 19.5 L 401.63 16 L 399.88 12.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 7px; margin-left: 289px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">6 bytes</div></div></div></foreignObject><text x="289" y="10" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">6 bytes</text></switch></g><path d="M 414.37 16 L 641.63 16" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 409.12 16 L 416.12 12.5 L 414.37 16 L 416.12 19.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 646.88 16 L 639.88 19.5 L 641.63 16 L 639.88 12.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 7px; margin-left: 529px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">6 bytes</div></div></div></foreignObject><text x="529" y="10" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">6 bytes</text></switch></g><path d="M 654.37 16 L 768 16" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 649.12 16 L 656.12 12.5 L 654.37 16 L 656.12 19.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 7px; margin-left: 708px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">6 bytes</div></div></div></foreignObject><text x="708" y="10" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">6 bytes</text></switch></g><path d="M 134.37 116 L 201.63 116" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 129.12 116 L 136.12 112.5 L 134.37 116 L 136.12 119.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 206.88 116 L 199.88 119.5 L 201.63 116 L 199.88 112.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 127px; margin-left: 169px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">2 bytes</div></div></div></foreignObject><text x="169" y="130" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">2 bytes</text></switch></g><path d="M 294.37 116 L 441.63 116" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 289.12 116 L 296.12 112.5 L 294.37 116 L 296.12 119.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 446.88 116 L 439.88 119.5 L 441.63 116 L 439.88 112.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 127px; margin-left: 368px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">4 bytes</div></div></div></foreignObject><text x="368" y="130" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">4 bytes</text></switch></g><path d="M 214.37 116 L 281.63 116" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 209.12 116 L 216.12 112.5 L 214.37 116 L 216.12 119.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 286.88 116 L 279.88 119.5 L 281.63 116 L 279.88 112.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 127px; margin-left: 249px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">2 bytes</div></div></div></foreignObject><text x="249" y="130" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">2 bytes</text></switch></g><path d="M 687.69 -13.68 L 727.69 -13.68 L 727.69 101.25 Q 717.69 93.25 707.69 101.25 Q 697.69 109.25 687.69 101.25 L 687.69 -9.24 Z" fill="#f5f5f5" stroke="#666666" stroke-miterlimit="10" transform="rotate(-90,707.69,46)" pointer-events="all"/><path d="M 48.32 26.32 L 88.32 26.32 L 88.32 141.25 Q 78.32 133.25 68.32 141.25 Q 58.32 149.25 48.32 141.25 L 48.32 30.76 Z" fill="#f5f5f5" stroke="#666666" stroke-miterlimit="10" transform="rotate(90,68.32,86)" pointer-events="all"/><rect x="677.69" y="36" width="60" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 46px; margin-left: 679px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">BSSID</div></div></div></foreignObject><text x="708" y="50" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">BSSID</text></switch></g><path d="M 8.32 116 L 121.63 116" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 126.88 116 L 119.88 119.5 L 121.63 116 L 119.88 112.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="38.32" y="76" width="60" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 86px; margin-left: 39px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">BSSID</div></div></div></foreignObject><text x="68" y="90" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">BSSID</text></switch></g><rect x="208" y="66" width="80" height="40" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 86px; margin-left: 209px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Reason code</div></div></div></foreignObject><text x="248" y="90" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Reason code</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Viewer does not support full SVG 1.1</text></a></switch></svg>