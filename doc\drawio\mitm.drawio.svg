<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="258px" height="218px" viewBox="-0.5 -0.5 258 218" content="&lt;mxfile&gt;&lt;diagram id=&quot;k75bTd7fJ-UVk2eBbEch&quot; name=&quot;Page-1&quot;&gt;7VjLktowEPwa3/0AB45A2M0hVG2FVCU5KvZgKxEeSpbXkK+PjEf4oYVQxSObrcABqTUz0nS3bcAJZuvto2SbdIExCMd3460TvHd833PHQ/1RIbsaeTd2ayCRPKagBljyX2AyCS14DHknUCEKxTddMMIsg0h1MCYllt2wFYrurhuWgAUsIyZs9AuPVVqjo6Hb4B+AJ6nZ2XNpZc1MMAF5ymIsW1Awd4KZRFT1aL2dgajIM7zUeQ9HVg8Hk5CpcxL8OuGZiYJ6e4Ss4BlocPJEZ1Q707jEIouhynWdYFqmXMFyw6JqtdRSayxVa6Fnnh6uuBAzFCj1PENdMpjSbiAVbI+e2DvwoA0EuAYldzqEEkbEHFknpGnZ0oGgtCWBCWOkfHKo25CjB8TPEXKHFh8Qa1/QFKVKMcGMiXmDTruMNTEfETfE0w9QakcmZ4XCLou5kvgTDI+OH7j7136FSTWp7KwXIsHynEcGfuDCFKgPXZ30NOO6MSxkRFEDuqyYTEC1vGLrIkEwxZ+71S/heWB58hMmReXIBVcLxw+FPtD0u9SjpBq9Tp/67t8z6uB1GHVYvW9t1NA26uBORg0toy4/T/4FL3p3NOPIIsk2ZxYbd1DPLUbOdYXdfKu54QvNGexsW9AOT8j1xgduwy61/qDHWe1VSmo/iU/X6Zep/W2V2bN/6PksQcZvXBBvdCVF+oVuJ4nnvXFNAvdKmvQL3VAT+6txlDL9e6IK+npKnt4TzoZbwsUsT/fPhstUNPd1+7Z+vzvfnS+zpo4JxNUqh4tlD07I/u2/7H/W68aX8nV019PmR3Yd3vxVEcx/Aw==&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <rect x="28" y="36" width="80" height="60" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 66px; margin-left: 29px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Genuine AP
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="68" y="70" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Genuine AP
                </text>
            </switch>
        </g>
        <path d="M 141.63 66 L 114.37 66" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 146.88 66 L 139.88 69.5 L 141.63 66 L 139.88 62.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 109.12 66 L 116.12 62.5 L 114.37 66 L 116.12 69.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="148" y="36" width="80" height="60" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 66px; margin-left: 149px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Rogue MitM
                                <br/>
                                AP
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="188" y="70" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Rogue MitM...
                </text>
            </switch>
        </g>
        <path d="M 188 129.63 L 188 102.37" fill="none" stroke="#050505" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 188 134.88 L 184.5 127.88 L 188 129.63 L 191.5 127.88 Z" fill="#050505" stroke="#050505" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 188 97.12 L 191.5 104.12 L 188 102.37 L 184.5 104.12 Z" fill="#050505" stroke="#050505" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="148" y="136" width="80" height="60" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 166px; margin-left: 149px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                STA
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="188" y="170" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    STA
                </text>
            </switch>
        </g>
        <path d="M 8 216 L 8 16" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 128 216 L 128 16" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 248 216 L 248 16" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 14.37 16 L 121.63 16" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 9.12 16 L 16.12 12.5 L 14.37 16 L 16.12 19.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 126.88 16 L 119.88 19.5 L 121.63 16 L 119.88 12.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 6px; margin-left: 68px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                channel X
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="68" y="9" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    channel X
                </text>
            </switch>
        </g>
        <path d="M 134.37 16 L 241.63 16" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 129.12 16 L 136.12 12.5 L 134.37 16 L 136.12 19.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 246.88 16 L 239.88 19.5 L 241.63 16 L 239.88 12.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 6px; margin-left: 188px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                channel Y
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="188" y="9" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    channel Y
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Viewer does not support full SVG 1.1
            </text>
        </a>
    </switch>
</svg>