<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="529px" height="231px" viewBox="-0.5 -0.5 529 231" content="&lt;mxfile&gt;&lt;diagram id=&quot;RgMXJTzvhfnaah8EVbSH&quot; name=&quot;Page-1&quot;&gt;1Vhdk5owFP01PrYDBPx4XNFuO9NOd9aZdvuYkSykGwkTo0J/fYMJX4ko22XRPsk9yYVw7jnJxRHwN+k9g0n0jQaIjBwrSEdgMXIc25p54idHMolMZpYEQoYDNakCVvgPKjIVusMB2jYmckoJx0kTXNM4RmvewCBj9NCc9kxJ86kJDJEBrNaQmOhPHPBIolPPqvDPCIdR8WTbUiMbWExWwDaCAT3UILAcAZ9RyuXVJvURyckreJF5n1pGy4UxFPMuCY5M2EOyU++2gByqtfGseGFGd3GA8hxrBOaHCHO0SuA6Hz2IEgss4hsiIltcPmNCfEooE3FMYzFpbi5LrXSPGEdpDVLLvEd0gzjLxJRitKBMaaak8FBVoJwT1dgvqgJV0cPy1hUv4kJR08KrZVCCAiEJFVLGIxrSGJJlhc6bpFVzvlKaKKp+I84zpW+447RJJEoxf1Lp+fWvGr5IawOLTAWtPG/pjq3VyoGE8hc4SzxDBHK8b2r+FIsq9YFi8dSyYI7bLBiwtEJwyELEVZZWi3IZncoDDBX7j/4tiljnZFARuwZLDwx9EDsQE6Q41gvKbpEy3feuNaTvbYOSK/neOut7QS/LjikfvSIsd4s8qNKO0av2C3VYS7tKaNz3FtK1IJ6h4S8/blG1utGHVa1zTdXaddVKOXbQbV21NRG/RbdjU7e2N8zZ5wLt7HPf7ewbG44wi0+I6IzRZS/AbSLb5Wec5lI4ZQ5IcBiLcC2oRwKf56bAoje+UwM810svHrJaGoiah6YnLDTtwUKTDqzGwV3+LVFRc2kL72oGexgzdN3Ea3R7Z3ast1pGq3ZpoeIW8m0My5g38jTZ6Hroz3uzvlRSKuPKR/3Nq6T8EiuKq5+XXVXizC7cqD+V2P9L0wK0VhsUKx+kaZmY3rnNpqWTk+zeW+TTOh7rX9nv12nYZqvh4yQ6yovfop5dnZtB9Tw12Hr6/mjQdOSuwcWWM/qCNDb+pRHb4CAgbV1fszwtzJ7gv/07fayRDcxuzT3BtfN6rkVY/UMqhVz9zwyWfwE=&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <rect x="0" y="0" width="120" height="50" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 25px; margin-left: 1px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Data
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="60" y="29" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Data
                </text>
            </switch>
        </g>
        <path d="M 120 50 L 120 73.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 120 78.88 L 116.5 71.88 L 120 73.63 L 123.5 71.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="120" y="0" width="120" height="50" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 25px; margin-left: 121px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                CRC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="180" y="29" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    CRC
                </text>
            </switch>
        </g>
        <rect x="0" y="180" width="120" height="50" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 205px; margin-left: 1px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Pre-shared key
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="60" y="209" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Pre-shared key
                </text>
            </switch>
        </g>
        <path d="M 120 180 L 120 166.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 120 161.12 L 123.5 168.12 L 120 166.37 L 116.5 168.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="120" y="180" width="120" height="50" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 205px; margin-left: 121px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                IV
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="180" y="209" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    IV
                </text>
            </switch>
        </g>
        <path d="M 160 120 L 193.63 120" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 198.88 120 L 191.88 123.5 L 193.63 120 L 191.88 116.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <ellipse cx="120" cy="120" rx="40" ry="40" fill="none" stroke="#000000" pointer-events="all"/>
        <path d="M 80 120 L 160 120" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 120 80 L 120 160" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <rect x="200" y="95" width="120" height="50" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 120px; margin-left: 201px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                IV
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="260" y="124" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    IV
                </text>
            </switch>
        </g>
        <path d="M 440 120 L 513.63 120" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 518.88 120 L 511.88 123.5 L 513.63 120 L 511.88 116.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="320" y="95" width="120" height="50" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 120px; margin-left: 321px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Ciphertext
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="380" y="124" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Ciphertext
                </text>
            </switch>
        </g>
        <rect x="40" y="110" width="40" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 120px; margin-left: 41px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                XOR
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="60" y="124" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    XOR
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Viewer does not support full SVG 1.1
            </text>
        </a>
    </switch>
</svg>