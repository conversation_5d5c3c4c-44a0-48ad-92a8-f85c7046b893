<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="391px" height="321px" viewBox="-0.5 -0.5 391 321" content="&lt;mxfile&gt;&lt;diagram id=&quot;_u18Hypegl6t3xsQ01c9&quot; name=&quot;WPA-handshake&quot;&gt;7Vpdb9ssFP41kbaLVLaJ0+QySbtuaztVzaR32s0rYpMYDRsL46bdrx/EYBvjtnGbpB/qVeAYDuY8z3nAkB6YxbdnDKbRJQ0R6XlOeNsDJz3Pc52xL36k5a6w+INhYVgxHKpGlWGO/yLdU1lzHKLMaMgpJRynpjGgSYICbtggY3RtNltSYo6awhWyDPMAEtv6Hw55VFhH3nFl/4rwKtIju8Nx8SSGurGaSRbBkK5rJnDaAzNGKS9K8e0MERk8HZff+fqvk/7uLwe/omv//+/frsNBv3D2pUuXcgoMJXy3rkHh+gaSXMVr5HhH7q+eNyRiqOmCidJKluZ5mhIcQPEGRTT4nQ6xCEwqi3lMLvASEZyI2jRFDMeIIyaeEGW+qmzTdYQ5mqcwkF3XgnzCFvGYiJorioIPHIourKwTAtMMLzajOsLCUJCzDN+ga5QVtJNWmnM50qykkzTq8U9gFqFQGZeYkBklVI6Q0M07Q4JXiagGItCbl9wy8gqhG8Q4uq3xTiFxhqiYNbsTTdRTX3FKJdVAVdcVQ12dQFGNnUBzEaqsWJWeK+RFQYHfgQjewGLCOZIvjJOMQxF6jmliIV/DywqdDIbgC5moB5xKhBnNk3ADwabTIqMk52jCAqUcG2tVkziFddBWBGaZKm8JDugMjoFNqWR1cJw2cPaFjQ2NTlJnkvNIzF7EmQsefyTmsxMTAO/IxL8tN1vh31tuagLWCPBQJjYTb0E5p7EMNlwgMoXBn9UmCxshRkk4keut7EFo8KcwfcFE++0W8ozmLECPs5pDtkJbpC8KjXXeBrCOz1BFjCEpXDfmVqANH+XuimK5vmkyeI4p0+64AXExSdWrvvY2HIHhI46KKFiOBCDwrtYslQ2y+18YgMY4A2NPIAqFx4qLZQCfQU+dZBU9TydX9KJfLCCfJj9oEqDPFmUloheSkqa+PLqQxDgMpQ+hM0JeYKU7KjrCuT/t+ScdKKsTrCkT5bZUjWLs/Nrko+8cuQYCfW8nTOyPG6rkmR7ocpkh3sB6N+h6H+LzIuIDRmYulwtRV/ER33BN8jRd7U5+7hlpvwJkf8W0CFDPE96dy2+zonD28/xVS5JOuzcgSQcQIXsL/A5ECGwpQoOXEyFbO56+BxodaA/ku63j7FeC/IclaG5K0KtWHp1rr1Z5POeAyjP8UJ5XojzA2ZHyWI52pDzePePsV3mOH1YeQbmEromEMJbkeRMSpJPuQ4JkNEYWwjNIglxMAAk2Olc/zx+SpAMdzHZTqA6HscfNDYhvH8jpS6qDHMe647eAxxMOyp9wWNr4RHVsaFznkNjo1bl+jSHkL5GwXJ5vxM+62FJbsxdHbG8ZZObPuA2jlvPs5hnB7jCyP9ebGDk2TJP3DhMA7pG3BVTl/cRhwLL3v+/lXvDZcgfabgZbr227oyOq1T8Lip1E9f8McPoP&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><rect x="0" y="0" width="110" height="40" fill="none" stroke="#000000" pointer-events="all"/><path d="M 55 40 L 55 320" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 20px; margin-left: 1px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">802.1X<br />Supplicant</div></div></div></foreignObject><text x="55" y="24" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">802.1X...</text></switch></g><rect x="5" y="210" width="100" height="30" rx="5" ry="5" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 217px; margin-left: 55px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">Key installation</div></div></div></foreignObject><text x="55" y="229" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Key installation</text></switch></g><rect x="282.5" y="0" width="100" height="40" fill="none" stroke="#000000" pointer-events="all"/><path d="M 332.5 40 L 332.5 320" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 20px; margin-left: 284px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">802.1X Authenticator</div></div></div></foreignObject><text x="333" y="24" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">802.1X Authentic...</text></switch></g><path d="M 332 100 L 290 100 Q 280 100 270 100 L 62.62 100" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 55.62 100 L 62.62 96.5 L 62.62 103.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 101px; margin-left: 198px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">EAPoL-Key (ANonce)</div></div></div></foreignObject><text x="198" y="104" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">EAPoL-Key (ANonce)</text></switch></g><path d="M 332 200 L 290.5 200 Q 280.5 200 270.5 200 L 62.62 200" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 55.62 200 L 62.62 196.5 L 62.62 203.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 199px; margin-left: 198px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">EAPoL-Key (ANonce, MIC, GTK)</div></div></div></foreignObject><text x="198" y="202" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">EAPoL-Key (ANonce, MIC, GTK)</text></switch></g><path d="M 54.5 150 L 291 150 Q 301 150 311 150 L 323.88 150" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 330.88 150 L 323.88 153.5 L 323.88 146.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 153px; margin-left: 201px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">EAPoL-Key (SNonce, MIC)</div></div></div></foreignObject><text x="201" y="156" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">EAPoL-Key (SNonce, MIC)</text></switch></g><path d="M 54.5 260 L 220 260 Q 230 260 240 260 L 323.88 260" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 330.88 260 L 323.88 263.5 L 323.88 256.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 263px; margin-left: 201px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">EAPoL-Key (acknowledgement, MIC)</div></div></div></foreignObject><text x="201" y="266" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">EAPoL-Key (acknowledgement, MIC)</text></switch></g><rect x="7.5" y="110" width="95" height="30" rx="5" ry="5" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 117px; margin-left: 55px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">Calculates PTK</div></div></div></foreignObject><text x="55" y="129" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Calculates PTK</text></switch></g><rect x="280" y="160" width="105" height="30" rx="5" ry="5" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 167px; margin-left: 333px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">Calculates PTK</div></div></div></foreignObject><text x="333" y="179" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Calculates PTK</text></switch></g><rect x="5" y="50" width="100" height="40" rx="5" ry="5" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 57px; margin-left: 55px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">Known PMK,<br />SNonce</div></div></div></foreignObject><text x="55" y="69" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Known PMK,...</text></switch></g><rect x="281.25" y="50" width="102.5" height="40" rx="5" ry="5" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 57px; margin-left: 333px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">Known PMK, <br />ANonce</div></div></div></foreignObject><text x="333" y="69" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Known PMK,...</text></switch></g><rect x="280" y="270" width="110" height="30" rx="5" ry="5" fill="#ffffff" stroke="#000000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 277px; margin-left: 335px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">Key installation</div></div></div></foreignObject><text x="335" y="289" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">Key installation</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Viewer does not support full SVG 1.1</text></a></switch></svg>