<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="161px" height="121px" viewBox="-0.5 -0.5 161 121" content="&lt;mxfile&gt;&lt;diagram id=&quot;yoLLaCjo7evepEMXJJgH&quot; name=&quot;MIC-generation&quot;&gt;5VfbcpswEP0aPyYDxtjOo+Ncp80k0/SWp44Ka1AtWEYsNs7XVwJhxJA4yYyTJlP8gPZoV5c9eyQz8OZJeS5ZFl9hCGIwdMJy4J0MhkPXOfLVSyObGvFH4xqIJA+NUwvc8ntoIg1a8BDyjiMhCuJZFwwwTSGgDsakxHXXbYGiO2vGIugBtwETffQHDymu0elw0uIXwKO4mdkdH9U9CWuczU7ymIW4tiDvdODNJSLVraScg9DJa/LybZoENFvds+8Xs8sl/7Up8i8H9WBnLwnZbkFCSvsdemi2RpsmXxCq9BkzxVS9jiUWaQh6GEdZKCnGCFMmPiNmCnQV+AeINoZ8VhAqKKZEmF4oOf3U4Ye+se7MYLp9UtrGpjFSkhsrSJt3dl8bVllNXE5M0kwXTruBCjvjQmwjwsYjECzPeVCDxkUv+ZkpN9TkWMgAdvh5pvKZjGDXeNPaT5Ngla8h9BwwAbVX5SBBMOKrbo0zI5Vo69eWg2qYinhBdZhVr5gozEy9clGiyHST1OkB96iHO85AcrUAkDZ+04LH65gT3GasStlaeXTLZcFLaE4Sbed103OqWiS1cUyVfeBOnRcztQJJUO7MrekdGdmbc29qzHV7iLiOwWLrAGlOi72TMfqHUnV3S1UZFr075et+SPn6z5Sv967k6/fke8OCpdrAYyJOkeBpef5Wg0RVoV0XJHjayDRkcnmtojhVZB46fqVlIeYoUFYzeQtf/ypOJS7B6hlXj47AlCy8ft5O58Nn6nz0Wjofv63O7Qt5q/mHdf4+tTn5kNqc9LX59VOPeVXC1OWtqxtDgi0yAzHBI31JBirJ1Z2rBcHV3+KZ6Uh4GIrH1N4tr9dSnjt+8oodveUNO+1xcnU5/884mXQpcR84DvfEiTLbb6eqz/oC9U7/Ag==&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><path d="M 50 80 L 50 93.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 50 98.88 L 46.5 91.88 L 50 93.63 L 53.5 91.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 0 80 L 30 60 L 70 60 L 100 80 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" transform="rotate(-180,50,70)" pointer-events="all"/><path d="M 50 40 L 50 53.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 50 58.88 L 46.5 51.88 L 50 53.63 L 53.5 51.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 0 0 L 70 0 L 100 30 L 100 40 L 0 40 L 0 0 Z" fill="#f5f5f5" stroke="#666666" stroke-miterlimit="10" pointer-events="all"/><path d="M 70 0 L 70 30 L 100 30 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="all"/><path d="M 70 0 L 70 30 L 100 30" fill="none" stroke="#666666" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 20px; margin-left: 1px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #333333; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Packet</div></div></div></foreignObject><text x="50" y="24" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">Packet</text></switch></g><path d="M 120 70 L 91.37 70" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 86.12 70 L 93.12 66.5 L 91.37 70 L 93.12 73.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="120" y="60" width="40" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 70px; margin-left: 121px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">PTK</div></div></div></foreignObject><text x="140" y="74" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">PTK</text></switch></g><rect x="30" y="100" width="40" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 110px; margin-left: 31px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">MIC</div></div></div></foreignObject><text x="50" y="114" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">MIC</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Viewer does not support full SVG 1.1</text></a></switch></svg>