<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="341px" height="121px" viewBox="-0.5 -0.5 341 121" content="&lt;mxfile&gt;&lt;diagram id=&quot;oVpvG1pEaaIT5wkvEVWg&quot; name=&quot;WPA-keys-hiearchy&quot;&gt;5VjbctowEP0aHjPje+CRQEjaNB06TieXN8WSbTVCy8gi4Hx9ZVsGa0SbpBMGSuAB75FWZs/Z1cru+aPZ6kKgeX4NmLCe5+BVzx/3PM91BqH6qZCyQcIgaoBMUKwnbYCYvpDWU6MLiklhTJQATNK5CSbAOUmkgSEhYGlOS4GZd52jjFhAnCBmo7cUy7xB+97pBr8kNMvbO7vRoBmZoXayjqTIEYZlB/LPe/5IAMjmarYaEVaR1/Li87gsLoPw9OrxyUcv0x/uw8tJs9jkPS7rEATh8p+Xfpjkl+XXnzRKAdLxTcjdaKFdnGfEFpqvKSqKk3kuUEF02LJsuRSw4JhU6zk9/2yZU0niOUqq0aXKHoXlcsaU5arLlDI2Agai9vXTNPWSROGFFPBEOiM4eozCSI28MUxNxzMRkqw6IuuwLwjMiBSlmqJHIx2lzuC+NpebdGhn5J1MCDSGdAJm63U3JKsLzfM7OHctzuP4y/hjyQ6r7zayo/pTeQCXHbz57FAE1zswFSJLhsAZREcvQ//AZDi1ZPDCo1dhzeeBqOCFrzAOQuaQAUfsG8Bc8/yLSFnqjo8WEkwVyIrKO+1eXd938PGqMzAuW4OrWO66xn3X2DjVVuuFUZHX/7NenONhdWhQJgdOGmRCKzbqye8TtICFSMjfsrevTzRIZES+3mcJNg4rdn4IwpCkz+bZ5eOLrm+3/eurY2n3wb6Lyd6+Kt1jbeq83Gd9uf9NfQ3eWF9tGzmUAhtYBTb8Djz54CP14TU219l38dlPNPHnYD7aN/P2c831UC3lDKfHzv36FcHeuPe2N/TJNLbbugpKmgybTOqNvUu7hhCjGVdmovgjCj+rKKIJYkM9MKMYV7fZKqkp+u52IONkPbCVCbYo4+1MGd9W5ubTaFKamuxNhOAPW1N8Mzz6vWl3z5bK3Lz9rMc675D9898=&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <rect x="60" y="80" width="60" height="40" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 100px; margin-left: 61px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Pass-phrase
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="90" y="104" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Pass-phrase
                </text>
            </switch>
        </g>
        <rect x="120" y="80" width="60" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 100px; margin-left: 121px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #333333; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                SSID
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="150" y="104" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    SSID
                </text>
            </switch>
        </g>
        <rect x="180" y="80" width="60" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 100px; margin-left: 181px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #333333; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                4096
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="210" y="104" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    4096
                </text>
            </switch>
        </g>
        <rect x="240" y="80" width="60" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 100px; margin-left: 241px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #333333; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                256
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="270" y="104" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    256
                </text>
            </switch>
        </g>
        <path d="M 40 40 L 60 80" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <rect x="40" y="0" width="60" height="40" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 20px; margin-left: 41px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                PMK
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="70" y="24" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    PMK
                </text>
            </switch>
        </g>
        <path d="M 100 40 L 300 80" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <rect x="100" y="0" width="60" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 20px; margin-left: 101px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #333333; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                ANonce
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="130" y="24" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ANonce
                </text>
            </switch>
        </g>
        <rect x="160" y="0" width="60" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 20px; margin-left: 161px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #333333; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                SNonce
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="190" y="24" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    SNonce
                </text>
            </switch>
        </g>
        <rect x="220" y="0" width="60" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 20px; margin-left: 221px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #333333; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                MAC AP
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="250" y="24" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    MAC AP
                </text>
            </switch>
        </g>
        <rect x="10" y="90" width="40" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 100px; margin-left: 11px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                PMK/PSK
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="30" y="104" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    PMK/PSK
                </text>
            </switch>
        </g>
        <rect x="0" y="10" width="40" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 20px; margin-left: 1px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                PTK
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="20" y="24" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    PTK
                </text>
            </switch>
        </g>
        <rect x="280" y="0" width="60" height="40" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 20px; margin-left: 281px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #333333; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                MAC STA
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="310" y="24" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    MAC STA
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Viewer does not support full SVG 1.1
            </text>
        </a>
    </switch>
</svg>